[flake8]
# Flake8代码风格检查配置

# 基础配置
max-line-length = 88
max-complexity = 10
select = E,W,F,C,B,B9

# 忽略的错误代码
ignore =
    E203,
    E501,
    W503,
    E231,
    B008,
    B902

# 排除的文件和目录
exclude =
    .git,
    __pycache__,
    .pytest_cache,
    .mypy_cache,
    .venv,
    venv,
    env,
    ENV,
    .env,
    build,
    dist,
    *.egg-info,
    alembic/versions,
    temp,
    workspace,
    reports

# 每个文件的错误数量限制
per-file-ignores =
    __init__.py:F401,F403
    tests/*:S101,S106,S311
    alembic/*:E402

# 文档字符串检查
docstring-convention = google

# 导入检查
application-import-names = app
import-order-style = google

# 复杂度检查
max-cognitive-complexity = 12

# 命名约定
classmethod-decorators = classmethod,pydantic.validator
staticmethod-decorators = staticmethod,pydantic.root_validator

# 输出格式
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s
show-source = True
statistics = True
count = True
