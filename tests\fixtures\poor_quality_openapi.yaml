openapi: 3.0.3
info:
  title: Poor Quality API
  version: 1.0.0
  # 缺少description

# 缺少servers配置

paths:
  /users:
    get:
      # 缺少summary和description
      responses:
        '200':
          description: OK
          # 缺少content和schema定义
    post:
      summary: Create user
      # 缺少description
      # 缺少requestBody
      responses:
        '201':
          description: Created
          # 缺少content和schema
        # 缺少错误响应

  /users/{id}:
    get:
      summary: Get user
      parameters:
        - name: id
          in: path
          required: true
          # 缺少schema定义
      responses:
        '200':
          description: User found
          content:
            application/json:
              # 缺少schema定义
              pass
        # 缺少404和500错误响应
    
    put:
      # 缺少summary和description
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string  # 应该是integer
      # 缺少requestBody
      responses:
        '200':
          description: Updated
          # 缺少content

  /products:
    # 只有路径，没有任何操作
    pass

# 缺少components定义
# 缺少security定义
# 缺少tags定义