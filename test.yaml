openapi: 3.0.3
info:
  title: Inkflow AI 小说接口文档
  description: 提供小说章节生成、用户管理、计划保存等服务
  version: 1.0.0
servers:
  - url: https://api.inkflow.ai/v1

paths:
  /chapter/generate:
    post:
      summary: 生成小说章节
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                prompt:
                  type: string
                max_tokens:
                  type: integer
                  default: 2000
      responses:
        '200':
          description: 成功返回生成内容
          content:
            application/json:
              schema:
                type: object
                properties:
                  chapter:
                    type: string

  /chapter/choices:
    post:
      summary: 获取下一章节选项
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                chapter_id:
                  type: string
      responses:
        '200':
          description: 返回章节分支选项
          content:
            application/json:
              schema:
                type: object
                properties:
                  choices:
                    type: array
                    items:
                      type: string

  /user/register:
    post:
      summary: 注册新用户
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_id:
                    type: string

  /user/login:
    post:
      summary: 用户登录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string

  /user/profile:
    get:
      summary: 获取用户信息
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 返回用户信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_id:
                    type: string
                  email:
                    type: string
                  created_at:
                    type: string

  /plan/save:
    post:
      summary: 保存用户创作计划
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                summary:
                  type: string
      responses:
        '200':
          description: 保存成功

  /plan/list:
    get:
      summary: 获取用户计划列表
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 返回创作计划列表
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    plan_id:
                      type: string
                    title:
                      type: string

  /plan/{plan_id}:
    get:
      summary: 获取指定计划详情
      security:
        - bearerAuth: []
      parameters:
        - name: plan_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 返回计划详情
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                  summary:
                    type: string

  /feedback:
    post:
      summary: 提交用户反馈
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                email:
                  type: string
      responses:
        '200':
          description: 提交成功

  /health:
    get:
      summary: 系统健康检查
      responses:
        '200':
          description: 返回健康状态
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
