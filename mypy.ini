[mypy]
# MyPy类型检查配置

# 基础配置
python_version = 3.12
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# 严格模式选项
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# 输出配置
show_error_codes = True
show_column_numbers = True
show_error_context = True
pretty = True
color_output = True
error_summary = True

# 缓存配置
cache_dir = .mypy_cache
sqlalchemy_plugin = True

# 第三方库配置
[mypy-pytest.*]
ignore_missing_imports = True

[mypy-uvicorn.*]
ignore_missing_imports = True

[mypy-fastapi.*]
ignore_missing_imports = True

[mypy-pydantic.*]
ignore_missing_imports = True

[mypy-sqlalchemy.*]
ignore_missing_imports = True

[mypy-alembic.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-loguru.*]
ignore_missing_imports = True

[mypy-click.*]
ignore_missing_imports = True

[mypy-typer.*]
ignore_missing_imports = True

[mypy-rich.*]
ignore_missing_imports = True

[mypy-yaml.*]
ignore_missing_imports = True

[mypy-jinja2.*]
ignore_missing_imports = True

[mypy-httpx.*]
ignore_missing_imports = True

[mypy-requests.*]
ignore_missing_imports = True

[mypy-openai.*]
ignore_missing_imports = True

[mypy-langchain.*]
ignore_missing_imports = True

[mypy-tiktoken.*]
ignore_missing_imports = True
