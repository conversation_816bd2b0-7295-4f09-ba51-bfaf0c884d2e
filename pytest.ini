[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts =
    -v
    --tb=short
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:reports/coverage
    --cov-report=xml:reports/coverage.xml
    --cov-fail-under=80
    --junit-xml=reports/junit.xml

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    parser: 解析器相关测试
    generator: 生成器相关测试
    executor: 执行器相关测试
    reporter: 报告器相关测试
    api: API相关测试
    database: 数据库相关测试
    llm: LLM相关测试
    performance: 性能测试
    compatibility: 兼容性测试
    error_handling: 错误处理测试
    security: 安全测试
    load: 负载测试
    stress: 压力测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pydantic.*

# 最小版本要求
minversion = 7.0

# 测试会话配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 异步测试支持
asyncio_mode = auto
