openapi: 3.0.3
info:
  title: Sample Pet Store API
  description: A sample API for testing OpenAPI parser
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.example.com/v1
    description: Production server
  - url: https://staging-api.example.com/v1
    description: Staging server

paths:
  /pets:
    get:
      summary: List all pets
      description: Returns a list of all pets in the store
      operationId: listPets
      tags:
        - pets
      parameters:
        - name: limit
          in: query
          description: How many items to return at one time (max 100)
          required: false
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of items to skip
          required: false
          schema:
            type: integer
            format: int32
            minimum: 0
            default: 0
      responses:
        '200':
          description: A paged array of pets
          headers:
            x-next:
              description: A link to the next page of responses
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pets'
              examples:
                sample:
                  summary: Sample pets response
                  value:
                    - id: 1
                      name: Fluffy
                      tag: cat
                    - id: 2
                      name: Buddy
                      tag: dog
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Create a pet
      description: Creates a new pet in the store
      operationId: createPet
      tags:
        - pets
      requestBody:
        description: Pet to add to the store
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewPet'
            examples:
              sample:
                summary: Sample pet creation
                value:
                  name: Max
                  tag: dog
      responses:
        '201':
          description: Pet created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pet'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /pets/{petId}:
    get:
      summary: Get a pet by ID
      description: Returns a single pet
      operationId: getPetById
      tags:
        - pets
      parameters:
        - name: petId
          in: path
          description: ID of pet to return
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        '200':
          description: Pet details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pet'
              examples:
                sample:
                  summary: Sample pet
                  value:
                    id: 1
                    name: Fluffy
                    tag: cat
        '404':
          description: Pet not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update a pet
      description: Updates an existing pet
      operationId: updatePet
      tags:
        - pets
      parameters:
        - name: petId
          in: path
          description: ID of pet to update
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        description: Updated pet information
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewPet'
      responses:
        '200':
          description: Pet updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pet'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Pet not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    delete:
      summary: Delete a pet
      description: Deletes a pet from the store
      operationId: deletePet
      tags:
        - pets
      parameters:
        - name: petId
          in: path
          description: ID of pet to delete
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        '204':
          description: Pet deleted successfully
        '404':
          description: Pet not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    Pet:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier for the pet
          example: 1
        name:
          type: string
          description: Name of the pet
          example: Fluffy
          minLength: 1
          maxLength: 50
        tag:
          type: string
          description: Tag to categorize the pet
          example: cat
          maxLength: 20
        status:
          type: string
          description: Pet status in the store
          enum:
            - available
            - pending
            - sold
          default: available

    NewPet:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          description: Name of the pet
          example: Max
          minLength: 1
          maxLength: 50
        tag:
          type: string
          description: Tag to categorize the pet
          example: dog
          maxLength: 20
        status:
          type: string
          description: Pet status in the store
          enum:
            - available
            - pending
            - sold
          default: available

    Pets:
      type: array
      items:
        $ref: '#/components/schemas/Pet'
      description: List of pets

    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
          description: Error code
          example: 400
        message:
          type: string
          description: Error message
          example: Invalid request
        details:
          type: string
          description: Additional error details
          example: The 'name' field is required

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for authentication
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token authentication

security:
  - ApiKeyAuth: []
  - BearerAuth: []

tags:
  - name: pets
    description: Everything about pets
    externalDocs:
      description: Find out more
      url: https://example.com/docs/pets

externalDocs:
  description: Find out more about our API
  url: https://example.com/docs