# =============================================================================
# Spec2Test 环境变量配置模板
# =============================================================================
# 复制此文件为 .env 并根据实际环境修改相应的值
# cp .env.example .env

# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME=Spec2Test
APP_VERSION=0.1.0
DEBUG=false

# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# CORS配置
CORS_ORIGINS=["*"]
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传配置
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=[".yaml",".yml",".json"]

# 工作目录配置
WORK_DIR=./workspace
TEMP_DIR=./temp

# =============================================================================
# LLM配置
# =============================================================================
# 服务提供商选择 (openai 或 gemini)
LLM_PROVIDER=gemini

# OpenAI配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_BASE_URL=

# Gemini配置
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-2.5-flash

# 生成参数
LLM_MAX_TOKENS=2000
LLM_TEMPERATURE=0.1
LLM_TIMEOUT=60

# 重试配置
LLM_MAX_RETRIES=3
LLM_RETRY_DELAY=1.0

# =============================================================================
# 测试配置
# =============================================================================
# 执行配置
TEST_TIMEOUT=30
TEST_MAX_RETRIES=2
TEST_PARALLEL_WORKERS=4

# 输出配置
TEST_OUTPUT_DIR=./test_output
TEST_KEEP_CODE=true

# 测试用例生成配置
TEST_MAX_CASES_PER_ENDPOINT=10
TEST_INCLUDE_EDGE_CASES=true
TEST_INCLUDE_SECURITY=true

# 报告配置
TEST_REPORT_FORMATS=["html","json"]
TEST_INCLUDE_AI_ANALYSIS=true

# =============================================================================
# 数据库配置
# =============================================================================
DB_DRIVER=sqlite
DB_HOST=localhost
DB_PORT=5432
DB_NAME=spec2test.db
DB_USER=your-db-user
DB_PASSWORD=your-db-password

# 连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# 其他数据库配置
DB_ECHO=false
DB_ECHO_POOL=false

# =============================================================================
# 日志配置
# =============================================================================
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志格式
LOG_FORMAT={time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}

# 文件日志配置
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log
LOG_FILE_ROTATION=10 MB
LOG_FILE_RETENTION=30 days

# 控制台日志配置
LOG_CONSOLE_ENABLED=true
LOG_CONSOLE_COLORIZE=true

# 结构化日志
LOG_JSON_FORMAT=false

# 敏感信息过滤
LOG_MASK_SENSITIVE=true
LOG_SENSITIVE_FIELDS=["password","token","api_key","secret"]

# =============================================================================
# 环境特定配置示例
# =============================================================================
# 开发环境
# DEV_AUTO_RELOAD=true
# DEV_SHOW_DOCS=true

# 生产环境
# PROD_SECRET_KEY=your-super-secret-production-key
# PROD_DB_HOST=your-production-db-host
# PROD_OPENAI_API_KEY=your-production-openai-key
# PROD_GEMINI_API_KEY=your-production-gemini-key

# 测试环境
# TEST_DB_NAME=spec2test_test.db
# TEST_LOG_LEVEL=DEBUG
