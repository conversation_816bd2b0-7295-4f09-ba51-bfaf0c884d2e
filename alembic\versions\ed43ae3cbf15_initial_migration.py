"""Initial migration

Revision ID: ed43ae3cbf15
Revises:
Create Date: 2025-07-30 07:11:34.096622

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "ed43ae3cbf15"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "documents",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("file_path", sa.String(length=500), nullable=False),
        sa.Column("file_hash", sa.String(length=64), nullable=False),
        sa.Column("file_size", sa.Integer(), nullable=False),
        sa.Column("mime_type", sa.String(length=100), nullable=True),
        sa.Column("document_type", sa.String(length=50), nullable=False),
        sa.Column("version", sa.String(length=20), nullable=True),
        sa.Column("quality_score", sa.Float(), nullable=True),
        sa.Column(
            "quality_level",
            sa.Enum("EXCELLENT", "GOOD", "FAIR", "POOR", name="documentquality"),
            nullable=True,
        ),
        sa.Column("total_endpoints", sa.Integer(), nullable=True),
        sa.Column("documented_endpoints", sa.Integer(), nullable=True),
        sa.Column("missing_descriptions", sa.Integer(), nullable=True),
        sa.Column("missing_examples", sa.Integer(), nullable=True),
        sa.Column("missing_schemas", sa.Integer(), nullable=True),
        sa.Column("analysis_result", sa.JSON(), nullable=True),
        sa.Column("issues", sa.JSON(), nullable=True),
        sa.Column("suggestions", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("analyzed_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_document_hash_type",
        "documents",
        ["file_hash", "document_type"],
        unique=False,
    )
    op.create_index(
        "idx_document_quality",
        "documents",
        ["quality_level", "quality_score"],
        unique=False,
    )
    op.create_index(
        op.f("ix_documents_file_hash"), "documents", ["file_hash"], unique=False
    )
    op.create_index(op.f("ix_documents_id"), "documents", ["id"], unique=False)
    op.create_index(op.f("ix_documents_name"), "documents", ["name"], unique=False)
    op.create_table(
        "execution_history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("execution_id", sa.String(length=255), nullable=False),
        sa.Column("execution_type", sa.String(length=50), nullable=False),
        sa.Column("target_id", sa.String(length=255), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("duration", sa.Float(), nullable=True),
        sa.Column("execution_params", sa.JSON(), nullable=True),
        sa.Column("result_summary", sa.JSON(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("error_details", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_execution_target", "execution_history", ["target_id"], unique=False
    )
    op.create_index(
        "idx_execution_time", "execution_history", ["start_time"], unique=False
    )
    op.create_index(
        "idx_execution_type_status",
        "execution_history",
        ["execution_type", "status"],
        unique=False,
    )
    op.create_index(
        op.f("ix_execution_history_execution_id"),
        "execution_history",
        ["execution_id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_execution_history_id"), "execution_history", ["id"], unique=False
    )
    op.create_table(
        "endpoints",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("document_id", sa.Integer(), nullable=False),
        sa.Column("path", sa.String(length=500), nullable=False),
        sa.Column(
            "method",
            sa.Enum(
                "GET",
                "POST",
                "PUT",
                "DELETE",
                "PATCH",
                "HEAD",
                "OPTIONS",
                name="httpmethod",
            ),
            nullable=False,
        ),
        sa.Column("operation_id", sa.String(length=255), nullable=True),
        sa.Column("summary", sa.String(length=500), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("tags", sa.JSON(), nullable=True),
        sa.Column("path_parameters", sa.JSON(), nullable=True),
        sa.Column("query_parameters", sa.JSON(), nullable=True),
        sa.Column("header_parameters", sa.JSON(), nullable=True),
        sa.Column("request_body", sa.JSON(), nullable=True),
        sa.Column("request_examples", sa.JSON(), nullable=True),
        sa.Column("responses", sa.JSON(), nullable=True),
        sa.Column("response_examples", sa.JSON(), nullable=True),
        sa.Column("security", sa.JSON(), nullable=True),
        sa.Column("deprecated", sa.Boolean(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["documents.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "document_id", "path", "method", name="uq_endpoint_path_method"
        ),
    )
    op.create_index("idx_endpoint_document", "endpoints", ["document_id"], unique=False)
    op.create_index(
        "idx_endpoint_path_method", "endpoints", ["path", "method"], unique=False
    )
    op.create_index(op.f("ix_endpoints_id"), "endpoints", ["id"], unique=False)
    op.create_index(
        op.f("ix_endpoints_operation_id"), "endpoints", ["operation_id"], unique=False
    )
    op.create_index(op.f("ix_endpoints_path"), "endpoints", ["path"], unique=False)
    op.create_table(
        "test_suites",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("suite_id", sa.String(length=255), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("document_id", sa.Integer(), nullable=True),
        sa.Column("base_url", sa.String(length=500), nullable=True),
        sa.Column("environment", sa.String(length=50), nullable=True),
        sa.Column("timeout", sa.Integer(), nullable=True),
        sa.Column("parallel_execution", sa.Boolean(), nullable=True),
        sa.Column("max_workers", sa.Integer(), nullable=True),
        sa.Column("retry_count", sa.Integer(), nullable=True),
        sa.Column("tags", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["documents.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_test_suite_document", "test_suites", ["document_id"], unique=False
    )
    op.create_index(
        "idx_test_suite_environment", "test_suites", ["environment"], unique=False
    )
    op.create_index(op.f("ix_test_suites_id"), "test_suites", ["id"], unique=False)
    op.create_index(
        op.f("ix_test_suites_suite_id"), "test_suites", ["suite_id"], unique=True
    )
    op.create_table(
        "test_cases",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("case_id", sa.String(length=255), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "type",
            sa.Enum("NORMAL", "ERROR", "EDGE", "SECURITY", name="testcasetype"),
            nullable=False,
        ),
        sa.Column("test_suite_id", sa.Integer(), nullable=False),
        sa.Column("endpoint_id", sa.Integer(), nullable=False),
        sa.Column("test_data", sa.JSON(), nullable=True),
        sa.Column("expected_status_code", sa.Integer(), nullable=True),
        sa.Column("expected_response", sa.JSON(), nullable=True),
        sa.Column("preconditions", sa.JSON(), nullable=True),
        sa.Column("postconditions", sa.JSON(), nullable=True),
        sa.Column("test_steps", sa.JSON(), nullable=True),
        sa.Column("priority", sa.Integer(), nullable=True),
        sa.Column("tags", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["endpoint_id"],
            ["endpoints.id"],
        ),
        sa.ForeignKeyConstraint(
            ["test_suite_id"],
            ["test_suites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_test_case_endpoint", "test_cases", ["endpoint_id"], unique=False
    )
    op.create_index(
        "idx_test_case_suite", "test_cases", ["test_suite_id"], unique=False
    )
    op.create_index(
        "idx_test_case_type_priority", "test_cases", ["type", "priority"], unique=False
    )
    op.create_index(
        op.f("ix_test_cases_case_id"), "test_cases", ["case_id"], unique=True
    )
    op.create_index(op.f("ix_test_cases_id"), "test_cases", ["id"], unique=False)
    op.create_table(
        "test_reports",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("report_id", sa.String(length=255), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("test_suite_id", sa.Integer(), nullable=False),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("duration", sa.Float(), nullable=True),
        sa.Column("total_tests", sa.Integer(), nullable=True),
        sa.Column("passed_tests", sa.Integer(), nullable=True),
        sa.Column("failed_tests", sa.Integer(), nullable=True),
        sa.Column("skipped_tests", sa.Integer(), nullable=True),
        sa.Column("error_tests", sa.Integer(), nullable=True),
        sa.Column("success_rate", sa.Float(), nullable=True),
        sa.Column("avg_response_time", sa.Float(), nullable=True),
        sa.Column("max_response_time", sa.Float(), nullable=True),
        sa.Column("min_response_time", sa.Float(), nullable=True),
        sa.Column("environment", sa.String(length=50), nullable=True),
        sa.Column("base_url", sa.String(length=500), nullable=True),
        sa.Column("html_report_path", sa.String(length=500), nullable=True),
        sa.Column("json_report_path", sa.String(length=500), nullable=True),
        sa.Column("xml_report_path", sa.String(length=500), nullable=True),
        sa.Column("tags", sa.JSON(), nullable=True),
        sa.Column(
            "generated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["test_suite_id"],
            ["test_suites.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_test_report_environment", "test_reports", ["environment"], unique=False
    )
    op.create_index(
        "idx_test_report_success_rate", "test_reports", ["success_rate"], unique=False
    )
    op.create_index(
        "idx_test_report_suite_time",
        "test_reports",
        ["test_suite_id", "start_time"],
        unique=False,
    )
    op.create_index(op.f("ix_test_reports_id"), "test_reports", ["id"], unique=False)
    op.create_index(
        op.f("ix_test_reports_report_id"), "test_reports", ["report_id"], unique=True
    )
    op.create_table(
        "test_results",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("result_id", sa.String(length=255), nullable=False),
        sa.Column("test_case_id", sa.Integer(), nullable=False),
        sa.Column("test_report_id", sa.Integer(), nullable=True),
        sa.Column(
            "status",
            sa.Enum(
                "PENDING",
                "RUNNING",
                "PASSED",
                "FAILED",
                "SKIPPED",
                "ERROR",
                name="teststatus",
            ),
            nullable=False,
        ),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("duration", sa.Float(), nullable=True),
        sa.Column("actual_status_code", sa.Integer(), nullable=True),
        sa.Column("actual_response", sa.JSON(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("error_details", sa.JSON(), nullable=True),
        sa.Column("stack_trace", sa.Text(), nullable=True),
        sa.Column("assertions", sa.JSON(), nullable=True),
        sa.Column("response_time", sa.Float(), nullable=True),
        sa.Column("memory_usage", sa.Float(), nullable=True),
        sa.Column("screenshots", sa.JSON(), nullable=True),
        sa.Column("logs", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["test_case_id"],
            ["test_cases.id"],
        ),
        sa.ForeignKeyConstraint(
            ["test_report_id"],
            ["test_reports.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_test_result_case", "test_results", ["test_case_id"], unique=False
    )
    op.create_index(
        "idx_test_result_report", "test_results", ["test_report_id"], unique=False
    )
    op.create_index(
        "idx_test_result_status_time",
        "test_results",
        ["status", "start_time"],
        unique=False,
    )
    op.create_index(op.f("ix_test_results_id"), "test_results", ["id"], unique=False)
    op.create_index(
        op.f("ix_test_results_result_id"), "test_results", ["result_id"], unique=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """降级数据库结构"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_test_results_result_id"), table_name="test_results")
    op.drop_index(op.f("ix_test_results_id"), table_name="test_results")
    op.drop_index("idx_test_result_status_time", table_name="test_results")
    op.drop_index("idx_test_result_report", table_name="test_results")
    op.drop_index("idx_test_result_case", table_name="test_results")
    op.drop_table("test_results")
    op.drop_index(op.f("ix_test_reports_report_id"), table_name="test_reports")
    op.drop_index(op.f("ix_test_reports_id"), table_name="test_reports")
    op.drop_index("idx_test_report_suite_time", table_name="test_reports")
    op.drop_index("idx_test_report_success_rate", table_name="test_reports")
    op.drop_index("idx_test_report_environment", table_name="test_reports")
    op.drop_table("test_reports")
    op.drop_index(op.f("ix_test_cases_id"), table_name="test_cases")
    op.drop_index(op.f("ix_test_cases_case_id"), table_name="test_cases")
    op.drop_index("idx_test_case_type_priority", table_name="test_cases")
    op.drop_index("idx_test_case_suite", table_name="test_cases")
    op.drop_index("idx_test_case_endpoint", table_name="test_cases")
    op.drop_table("test_cases")
    op.drop_index(op.f("ix_test_suites_suite_id"), table_name="test_suites")
    op.drop_index(op.f("ix_test_suites_id"), table_name="test_suites")
    op.drop_index("idx_test_suite_environment", table_name="test_suites")
    op.drop_index("idx_test_suite_document", table_name="test_suites")
    op.drop_table("test_suites")
    op.drop_index(op.f("ix_endpoints_path"), table_name="endpoints")
    op.drop_index(op.f("ix_endpoints_operation_id"), table_name="endpoints")
    op.drop_index(op.f("ix_endpoints_id"), table_name="endpoints")
    op.drop_index("idx_endpoint_path_method", table_name="endpoints")
    op.drop_index("idx_endpoint_document", table_name="endpoints")
    op.drop_table("endpoints")
    op.drop_index(op.f("ix_execution_history_id"), table_name="execution_history")
    op.drop_index(
        op.f("ix_execution_history_execution_id"), table_name="execution_history"
    )
    op.drop_index("idx_execution_type_status", table_name="execution_history")
    op.drop_index("idx_execution_time", table_name="execution_history")
    op.drop_index("idx_execution_target", table_name="execution_history")
    op.drop_table("execution_history")
    op.drop_index(op.f("ix_documents_name"), table_name="documents")
    op.drop_index(op.f("ix_documents_id"), table_name="documents")
    op.drop_index(op.f("ix_documents_file_hash"), table_name="documents")
    op.drop_index("idx_document_quality", table_name="documents")
    op.drop_index("idx_document_hash_type", table_name="documents")
    op.drop_table("documents")
    # ### end Alembic commands ###
